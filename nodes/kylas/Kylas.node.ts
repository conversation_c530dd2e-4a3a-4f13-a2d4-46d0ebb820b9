import {
    IExecuteFunctions,
    INodeType,
    INodeTypeDescription,
    INodeExecutionData,
    NodeApiError,
    JsonObject,
    IDataObject,
    IHttpRequestMethods,
    IHookFunctions,
    ILoadOptionsFunctions,
    IRequestOptions,
    INodePropertyOptions
} from 'n8n-workflow';

// Cache for system fields to avoid repeated API calls
let systemFieldsCache: any = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Helper function to get cached system fields
async function getCachedSystemFields(this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions): Promise<any[]> {
    const now = Date.now();

    // Check if cache is valid (not expired)
    if (systemFieldsCache && (now - cacheTimestamp) < CACHE_DURATION) {
        console.log('Using cached system fields');
        return systemFieldsCache;
    }

    // Cache is expired or doesn't exist, fetch fresh data
    console.log('Fetching fresh system fields from API');
    const customFields = await kylasApiRequest.call(this, 'GET', '/v1/layouts/leads/system-fields?view=create', {});
    const fields = JSON.parse(customFields.data);

    // Update cache
    systemFieldsCache = fields;
    cacheTimestamp = now;

    return fields;
}

export class Kylas implements INodeType {
    description: INodeTypeDescription = {
        displayName: 'Kylas',
        name: 'kylas',
        icon: 'file:kylas2.svg', // Assumes a file named nasa.svg in the same directory
        group: ['transform'], // This makes it an 'input' node, good for fetching data
        version: 1,
        subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
        description: 'Access Kylas Data',
        defaults: {
            name: 'Kylas',
        },
        usableAsTool: true,
        inputs: ['main'],
        outputs: ['main'],
        credentials: [
            {
                name: 'kylasApi',
                required: true,
                displayOptions: {
                    show: {
                        authentication: ['apiToken'],
                    },
                }
            }
        ],
        properties: [
            {
                displayName: 'Authentication',
                name: 'authentication',
                type: 'options',
                options: [
                    {
                        name: 'API Token',
                        value: 'apiToken',
                    },
                ],
                default: 'apiToken',
            },
            {
                displayName: 'Resource',
                name: 'resource',
                noDataExpression: true,
                type: 'options',
                options: [
                    {
                        name: 'Lead',
                        value: 'lead',
                    },
                ],
                default: 'lead',
            },
            // --- Operations ---
            {

                displayName: 'Operation',
                name: 'operation',
                noDataExpression: true,
                displayOptions: {
                    show: {
                        resource: ['lead'],
                    }
                },
                type: 'options',
                options: [
                    {
                        name: 'Create',
                        value: 'create',
                        description: 'Create a lead',
                        action: 'Create a lead'
                    },
                ],
                default: 'create'
            },
            {
                displayName: "First Name",
                name: "firstName",
                description: 'First name of the lead',
                type: 'string',
                required: true,
                default: '',
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                }
            },
            {
                displayName: "Last Name",
                name: "lastName",
                description: 'Last name of the lead',
                type: 'string',
                required: true,
                default: '',
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                }
            },
            {
                displayName: "Phone Numbers",
                name: "phoneNumbers",
                description: 'Add phone numbers',
                type: 'fixedCollection',
                typeOptions: {
                    multipleValues: true,
                },
                default: {},
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                },
                options: [
                    {
                        displayName: "Phone Number",
                        name: "phoneNumber",
                        values: [
                            {
                                displayName: "Country Code",
                                name: "code",
                                type: "string",
                                default: "IN",
                                description: 'Country code (e.g., IN, US)',
                            },
                            {
                                displayName: "Dial Code",
                                name: "dialCode",
                                type: "string",
                                default: "+91",
                                description: 'Dial code with + prefix (e.g., +91, +1)',
                            },
                            {
                                displayName: "Phone Number",
                                name: "value",
                                type: "string",
                                default: '',
                                description: 'Phone number without country code',
                            },
                            {
                                displayName: "Primary",
                                name: "primary",
                                type: "boolean",
                                default: false,
                                description: 'Whether this is the primary phone number',
                            },
                            {
                                displayName: "Type",
                                name: "type",
                                type: "options",
                                options: [
                                    {
                                        name: "Mobile",
                                        value: "MOBILE",
                                    },
                                    {
                                        name: "Home",
                                        value: "HOME",
                                    },
                                    {
                                        name: "Work",
                                        value: "WORK",
                                    },
                                ],
                                default: "MOBILE",
                                description: 'Type of phone number',
                            }
                        ]
                    }
                ]
            },
            {
                displayName: "Custom Fields",
                name: "customFields",
                description: 'Add custom field',
                type: 'fixedCollection',
                typeOptions: {
                    multipleValues: true,
                },
                default: {},
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                },
                options: [
                    {
                        displayName: "Property",
                        name: "property",
                        values: [
                            {
                                displayName: "Field Name or ID",
                                name: "name",
                                type: "options",
                                typeOptions: {
                                    loadOptionsMethod: 'getLeadCustomFields',
                                },
                                default: '',
                                description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                            },
                            {
                                displayName: "Field Type",
                                name: "fieldType",
                                type: "hidden",
                                typeOptions: {
                                    loadOptionsMethod: 'getFieldType',
                                    loadOptionsDependsOn: ['name'],
                                },
                                default: '',
                            },
                            {
                                displayName: "Field Value Name or ID",
                                name: "value",
                                type: "options",
                                typeOptions: {
                                    loadOptionsMethod: 'getPicklistValues',
                                    loadOptionsDependsOn: ['name'],
                                },
                                default: '',
                                description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                                displayOptions: {
                                    show: {
                                        fieldType: ['PICK_LIST'],
                                    },
                                },
                            },
                            {
                                displayName: "Field Value",
                                name: "value",
                                type: "string",
                                default: '',
                                description: 'Enter a value for the field',
                                displayOptions: {
                                    hide: {
                                        fieldType: ['PICK_LIST'],
                                    },
                                },
                            }
                        ]
                    }
                ]
            },
            {

                displayName: 'Lead ID',
                name: 'leadId',
                noDataExpression: true,
                type: 'number',
                default: 530951,
                displayOptions: {
                    show: {
                        operation: ['getLeadById'],
                    }
                }
            },
        ],
    };
    methods = {
        loadOptions: {
            async getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                const returnData: INodePropertyOptions[] = [];
                const fields = await getCachedSystemFields.call(this);
                (fields as any[])
                .filter(field => field.active
                    && field.type !=='LOOK_UP'
                    && field.type !=='MULTI_PICKLIST')
                .forEach(field => {
                    // Add field type information to the display name for PICK_LIST fields
                    const displayName = field.type === 'PICK_LIST'
                        ? `${field.displayName} (Picklist)`
                        : field.displayName;
                    returnData.push({
                        name: displayName,
                        value: field.name
                    });
                });
                return returnData;
            },
            async getFieldType(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                const returnData: INodePropertyOptions[] = [];
                const selectedFieldValue = this.getCurrentNodeParameter('name') as string;

                if (!selectedFieldValue) {
                    return returnData;
                }

                try {
                    const fields = await getCachedSystemFields.call(this);

                    // Find the field by its name (value) since that's what we store in the dropdown
                    const selectedField = (fields as any[]).find(field => field.name === selectedFieldValue);

                    if (selectedField) {
                        returnData.push({
                            name: selectedField.type,
                            value: selectedField.type
                        });
                    }
                } catch (error) {
                    console.error('Error loading field type:', error);
                }

                return returnData;
            },
            async getPicklistValues(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                const returnData: INodePropertyOptions[] = [];
                const selectedFieldValue = this.getCurrentNodeParameter('name') as string;
                console.log("selectedFieldValue->" + selectedFieldValue);
                if (!selectedFieldValue) {
                    return returnData;
                }

                try {
                    const fields = await getCachedSystemFields.call(this);
                    console.log("Using cached fields for picklist values");

                    // Find the field by its name (value) since that's what we store in the dropdown
                    const selectedField = (fields as any[]).find(field => field.name === selectedFieldValue);

                    if (selectedField && selectedField.type === 'PICK_LIST' && selectedField.picklist && selectedField.picklist.values) {
                        selectedField.picklist.values.forEach((picklistValue: any) => {
                            if (!picklistValue.disabled) {
                                returnData.push({
                                    name: picklistValue.displayName,
                                    value: picklistValue.name
                                });
                            }
                        });
                    }
                } catch (error) {
                    console.error('Error loading picklist values:', error);
                }

                return returnData;
            }
        }
    };
    async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
        const items = this.getInputData();
        const returnData: INodeExecutionData[] = [];
        // const credentials = await this.getCredentials('kylasApi');
        // const apiKey = credentials.apiKey as string;
        const resource = this.getNodeParameter('resource', 0);
        let body: IDataObject;
        let requestMethod: IHttpRequestMethods;
        let endpoint = '';
        for (let i = 0; i < items.length; i++) {
            const operation = this.getNodeParameter('operation', i) as string;
            body = {};
            requestMethod = 'GET';
            endpoint = '';
            if (resource === 'lead') {
                if (operation === 'create') {
                    // Get basic fields
                    const firstName = this.getNodeParameter('firstName', i) as string;
                    const lastName = this.getNodeParameter('lastName', i) as string;

                    // Initialize body with basic fields
                    body = {
                        firstName,
                        lastName,
                    } as IDataObject;

                    // Handle phone numbers
                    const phoneNumbers = this.getNodeParameter('phoneNumbers', i) as IDataObject;
                    if (phoneNumbers && phoneNumbers.phoneNumber) {
                        const phoneNumberArray = phoneNumbers.phoneNumber as IDataObject[];
                        body.phoneNumbers = phoneNumberArray.map((phone: IDataObject) => ({
                            type: phone.type,
                            code: phone.code,
                            value: phone.value,
                            dialCode: phone.dialCode,
                            primary: phone.primary,
                        }));
                    }

                    // Handle custom fields - construct customFieldValues object
                    const customFields = this.getNodeParameter('customFields', i) as IDataObject;
                    if (customFields && customFields.property) {
                        const customFieldArray = customFields.property as IDataObject[];
                        const customFieldValues: IDataObject = {};

                        customFieldArray.forEach((field: IDataObject) => {
                            if (field.name && field.value) {
                                customFieldValues[field.name as string] = field.value;
                            }
                        });

                        if (Object.keys(customFieldValues).length > 0) {
                            body.customFieldValues = customFieldValues;
                        }
                    }

                    requestMethod = 'POST';
                    endpoint = "/v1/leads";
                }
            }
            console.log("body->" + JSON.stringify(body));
            let responseData = await kylasApiRequest.call(
                this,
                requestMethod,
                endpoint,
                body
            );
            console.log("responseData->" + JSON.stringify(responseData));
            const executionData = this.helpers.constructExecutionMetaData(
                this.helpers.returnJsonArray(responseData.data as IDataObject[]),
                { itemData: { item: i } },
            );
            returnData.push(...executionData);

            // if (operation === 'getLeadById') {
            //     const leadId = this.getNodeParameter('leadId', i) as number;
            //     try {
            //         const options: IHttpRequestOptions = {
            //             url: `https://api-qa.sling-dev.com/v1/leads/${leadId}`,
            //             headers: {
            //                 'api-key': `${apiKey}`,
            //             },
            //             json: true,
            //         };
            //         const response = await this.helpers.request(options);
            //         returnData.push({
            //             json: response,
            //             pairedItem: items[i].pairedItem,
            //         });
            //     } catch (error) {
            //         this.logger.error('Failed to fetch APOD data: ' + error.message);
            //         throw new NodeApiError(this.getNode(), error as JsonObject, {
            //             message: 'Failed to fetch APOD data. Please check your API key.',
            //         });
            //         // throw new Error('Failed to fetch APOD data. Please check your API key.');
            //     }
            // } 
        }

        return this.prepareOutputData(returnData);
    }
}

export async function kylasApiRequest(
    this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions,
    method: IHttpRequestMethods,
    endpoint: string,
    body: IDataObject
): Promise<any> {
    // const authenticationMethod = this.getNodeParameter('authentication', 0);
    console.log("URI ->" + `https://api-qa.sling-dev.com${endpoint}`);
    const options: IRequestOptions = {
        headers: {
            Accept: 'application/json',
        },
        method,
        uri: `https://api-qa.sling-dev.com${endpoint}`,
    };

    // console.log("options->" + JSON.stringify(options));

    if (Object.keys(body).length !== 0) {
        options.body = body;
        options.json = true;
    }


    try {
        const credentialType = 'kylasApi';
        // console.log("option->" + JSON.stringify(options.body));
        const responseData = await this.helpers.requestWithAuthentication.call(
            this,
            credentialType,
            options,
        );


        console.log("responseData 2->" + JSON.stringify(responseData));
        if (responseData.success === false) {
            throw new NodeApiError(this.getNode(), responseData as JsonObject);
        }
        console.log("Return success")
        return {
            data: responseData
        };
    } catch (error) {
        throw new NodeApiError(this.getNode(), error as JsonObject);
    }
}
