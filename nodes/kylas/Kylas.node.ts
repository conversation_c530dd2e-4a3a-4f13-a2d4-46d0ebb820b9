import {
    IExecuteFunctions,
    INodeType,
    INodeTypeDescription,
    INodeExecutionData,
    NodeApiError,
    JsonObject,
    IDataObject,
    IHttpRequestMethods,
    IHookFunctions,
    ILoadOptionsFunctions,
    IRequestOptions,
    INodePropertyOptions
} from 'n8n-workflow';

export class <PERSON><PERSON><PERSON> implements INodeType {
    description: INodeTypeDescription = {
        displayName: 'Kyla<PERSON>',
        name: 'kyla<PERSON>',
        icon: 'file:kylas2.svg', // Assumes a file named nasa.svg in the same directory
        group: ['transform'], // This makes it an 'input' node, good for fetching data
        version: 1,
        subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
        description: 'Access Kylas Data',
        defaults: {
            name: '<PERSON>yla<PERSON>',
        },
        usableAsTool: true,
        inputs: ['main'],
        outputs: ['main'],
        credentials: [
            {
                name: 'kylas<PERSON><PERSON>',
                required: true,
                displayOptions: {
                    show: {
                        authentication: ['apiToken'],
                    },
                }
            }
        ],
        properties: [
            {
                displayName: 'Authentication',
                name: 'authentication',
                type: 'options',
                options: [
                    {
                        name: 'API Token',
                        value: 'apiToken',
                    },
                ],
                default: 'apiToken',
            },
            {
                displayName: 'Resource',
                name: 'resource',
                noDataExpression: true,
                type: 'options',
                options: [
                    {
                        name: 'Lead',
                        value: 'lead',
                    },
                ],
                default: 'lead',
            },
            // --- Operations ---
            {

                displayName: 'Operation',
                name: 'operation',
                noDataExpression: true,
                displayOptions: {
                    show: {
                        resource: ['lead'],
                    }
                },
                type: 'options',
                options: [
                    {
                        name: 'Create',
                        value: 'create',
                        description: 'Create a lead',
                        action: 'Create a lead'
                    },
                ],
                default: 'create'
            },
            {
                displayName: "First Name",
                name: "firstName",
                description: 'First name of the lead',
                type: 'string',
                required: true,
                default: '',
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                }
            },
            {
                displayName: "Last Name",
                name: "lastName",
                description: 'Last name of the lead',
                type: 'string',
                required: true,
                default: '',
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                }
            },
            {
                displayName: "Additional Fields",
                name: "additionalFields",
                placeholder: 'Add more fields',
                type: 'collection',
                default: '',
                displayOptions: {
                    show: {
                        resource: ['lead'],
                        operation: ['create'],
                    }
                },
                options: [
                    {
                        displayName: "Address",
                        name: "address",
                        description: 'Address of the lead',
                        type: 'string',
                        required: false,
                        default: '',
                    },
                    {
                        displayName: "City",
                        name: "city",
                        description: 'City of the lead',
                        type: 'string',
                        required: false,
                        default: '',
                    },
                    {
                        displayName: "Custom Fields",
                        name: "customFields",
                        description: 'Add custom field',
                        type: 'fixedCollection',
                        typeOptions: {
                            multipleValues: true,
                        },
                        default: {},
                        options: [
                            {
                                displayName: "Property",
                                name: "property",
                                values: [
                                    {
                                        displayName: "Field Name",
                                        name: "name",
                                        type: "options",
                                        typeOptions: {
                                            loadOptionsMethod: 'getLeadCustomFields',
                                        },
                                        default: '',
                                        description: 'Select a property',
                                    },
                                    {
                                        displayName: "Field Value",
                                        name: "value",
                                        type: "string",
                                        default: '',
                                        description: 'Enter a value',
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {

                displayName: 'Lead ID',
                name: 'leadId',
                noDataExpression: true,
                type: 'number',
                default: 530951,
                displayOptions: {
                    show: {
                        operation: ['getLeadById'],
                    }
                }
            },
        ],
    };
    methods = {
        loadOptions: {
            async getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]> {
                const returnData: INodePropertyOptions[] = [];
                const customFields = await kylasApiRequest.call(this, 'GET', '/v1/layouts/leads/system-fields?view=create', {});
                const fields = JSON.parse(customFields.data);
                (fields as any[])
                .filter(field => field.active 
                    && field.type !=='PICK_LIST' 
                    && field.type !=='LOOK_UP' 
                    && field.type !=='MULTI_PICKLIST')
                .forEach(field => {
                    returnData.push({
                        name: field.displayName,
                        value: field.displayName
                    });
                });
                return returnData;
            }
        }
    };
    async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
        const items = this.getInputData();
        const returnData: INodeExecutionData[] = [];
        // const credentials = await this.getCredentials('kylasApi');
        // const apiKey = credentials.apiKey as string;
        const resource = this.getNodeParameter('resource', 0);
        let body: IDataObject;
        let requestMethod: IHttpRequestMethods;
        let endpoint = '';
        for (let i = 0; i < items.length; i++) {
            const operation = this.getNodeParameter('operation', i) as string;
            body = {};
            requestMethod = 'GET';
            endpoint = '';
            if (resource === 'lead') {
                if (operation === 'create') {
                    body = {
                        firstName: this.getNodeParameter('firstName', i) as string,
                    } as IDataObject;
                    requestMethod = 'POST';
                    endpoint = "/v1/leads"

                }
            }
            let responseData = await kylasApiRequest.call(
                this,
                requestMethod,
                endpoint,
                body
            );
            console.log("responseData->" + JSON.stringify(responseData));
            const executionData = this.helpers.constructExecutionMetaData(
                this.helpers.returnJsonArray(responseData.data as IDataObject[]),
                { itemData: { item: i } },
            );
            returnData.push(...executionData);

            // if (operation === 'getLeadById') {
            //     const leadId = this.getNodeParameter('leadId', i) as number;
            //     try {
            //         const options: IHttpRequestOptions = {
            //             url: `https://api-qa.sling-dev.com/v1/leads/${leadId}`,
            //             headers: {
            //                 'api-key': `${apiKey}`,
            //             },
            //             json: true,
            //         };
            //         const response = await this.helpers.request(options);
            //         returnData.push({
            //             json: response,
            //             pairedItem: items[i].pairedItem,
            //         });
            //     } catch (error) {
            //         this.logger.error('Failed to fetch APOD data: ' + error.message);
            //         throw new NodeApiError(this.getNode(), error as JsonObject, {
            //             message: 'Failed to fetch APOD data. Please check your API key.',
            //         });
            //         // throw new Error('Failed to fetch APOD data. Please check your API key.');
            //     }
            // } 
        }

        return this.prepareOutputData(returnData);
    }
}

export async function kylasApiRequest(
    this: IHookFunctions | IExecuteFunctions | ILoadOptionsFunctions,
    method: IHttpRequestMethods,
    endpoint: string,
    body: IDataObject
): Promise<any> {
    // const authenticationMethod = this.getNodeParameter('authentication', 0);
    console.log("URI ->" + `https://api-qa.sling-dev.com${endpoint}`);
    const options: IRequestOptions = {
        headers: {
            Accept: 'application/json',
        },
        method,
        uri: `https://api-qa.sling-dev.com${endpoint}`,
    };

    // console.log("options->" + JSON.stringify(options));

    if (Object.keys(body).length !== 0) {
        options.body = body;
        options.json = true;
    }


    try {
        const credentialType = 'kylasApi';
        // console.log("option->" + JSON.stringify(options.body));
        const responseData = await this.helpers.requestWithAuthentication.call(
            this,
            credentialType,
            options,
        );


        console.log("responseData 2->" + JSON.stringify(responseData));
        if (responseData.success === false) {
            throw new NodeApiError(this.getNode(), responseData as JsonObject);
        }
        console.log("Return success")
        return {
            data: responseData
        };
    } catch (error) {
        throw new NodeApiError(this.getNode(), error as JsonObject);
    }
}
